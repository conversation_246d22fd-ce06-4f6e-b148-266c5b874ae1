# 🔐 Management Bot Access Levels

## Overview

The Wiz-Aroma Management Bot now supports two levels of access control to provide secure and granular permissions for different administrative roles.

## Access Levels

### 1. General Management Access
**Authorized Users:** `7729984017`, `5546595738`

**Permissions Include:**
- ✅ Personnel Management (Add/Edit/Delete delivery personnel)
- ✅ Analytics & Reporting (Daily/Weekly/Monthly reports)
- ✅ Financial Reports (Revenue, profit tracking)
- ✅ Order Management (View orders, assignments)
- ✅ System Status Monitoring
- ✅ Audit Log Viewing (non-system operations)

**Restrictions:**
- ❌ System Management Operations (Reset/Cleanup)
- ❌ Seasonal Data Reset
- ❌ Global Order Reset
- ❌ Daily Cleanup Operations

### 2. System Administration Access
**Authorized Users:** `7729984017` (Primary Admin Only)

**Additional Permissions:**
- ✅ All General Management permissions
- ✅ System Management Operations
- ✅ Seasonal Data Reset
- ✅ Global Order Reset
- ✅ Daily Cleanup Operations
- ✅ System Configuration Changes

## Technical Implementation

### Authorization Functions

```python
# General Management Bot Access
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017, 5546595738]

# System Management - Primary Admin Only
SYSTEM_ADMIN_ID = 7729984017

def is_authorized_user(user_id):
    """Check if user can access management functions"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def is_authorized_for_reset(user_id):
    """Check if user can perform system operations"""
    return user_id == SYSTEM_ADMIN_ID
```

### Security Features

1. **Validation**: All user IDs are validated before authorization checks
2. **Logging**: All authorization attempts are logged for audit purposes
3. **Rate Limiting**: Protection against rapid successive requests
4. **Session Management**: Secure session handling for multi-step operations

## User Experience

### For General Management Users (5546595738)
- Full access to day-to-day management operations
- Cannot accidentally trigger system-wide resets
- Clear error messages when attempting restricted operations

### For System Administrator (7729984017)
- Complete access to all management functions
- Additional confirmation steps for destructive operations
- Enhanced audit logging for system-level changes

## Configuration Files Updated

1. **`.env`**: Updated `ADMIN_CHAT_IDS` to include both users
2. **`src/bots/management_bot.py`**: Modified authorization logic
3. **`docs/DATA_MANAGEMENT_API.md`**: Updated documentation

## Security Considerations

- System management operations remain restricted to prevent accidental data loss
- All authorization changes are logged for compliance
- Fallback mechanisms ensure system stability
- Clear separation between operational and administrative functions

## Testing

To verify access levels:

1. **General Management Test**: User `5546595738` should access personnel, analytics, reports
2. **System Management Test**: Only user `7729984017` should access reset/cleanup functions
3. **Error Handling Test**: Proper error messages for unauthorized access attempts

## Support

For access level modifications or issues:
- Contact: Primary System Administrator (`7729984017`)
- Documentation: This file and `DATA_MANAGEMENT_API.md`
- Logs: Check management bot logs for authorization events
