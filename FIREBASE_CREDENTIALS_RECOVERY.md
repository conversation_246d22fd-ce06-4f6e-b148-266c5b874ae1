# 🔥 Firebase Credentials Recovery Guide

## 🚨 Issue: Firebase Connection Failed

Your Wiz-Aroma system cannot connect to Firebase because the credentials file was removed during security cleanup. Here's how to fix it:

## 🔧 **Solution Options**

### **Option 1: Regenerate Firebase Credentials File (Recommended)**

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com/
   - Select your project: `wiz-aroma-adama`

2. **Generate New Service Account Key**
   - Click the gear icon (⚙️) → Project Settings
   - Go to "Service accounts" tab
   - Click "Generate new private key"
   - Download the JSON file

3. **Save the Credentials File**
   - Rename the downloaded file to: `wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json`
   - Place it in your project root directory (same folder as main.py)
   - **IMPORTANT**: Never commit this file to git (it's already in .gitignore)

### **Option 2: Use Environment Variable (More Secure)**

1. **Get Firebase Credentials JSON**
   - Follow steps 1-2 from Option 1 to download the JSON file

2. **Add to .env File**
   - Open your `.env` file
   - Find the line that starts with `# FIREBASE_CREDENTIALS=`
   - Remove the `#` and replace with your actual JSON:
   ```
   FIREBASE_CREDENTIALS={"type":"service_account","project_id":"wiz-aroma-adama",...}
   ```
   - **Note**: Put the entire JSON on one line

## 🧪 **Test Firebase Connection**

After setting up credentials, test the connection:

```bash
python setup_firebase_delivery_data.py
```

This script will:
- ✅ Test Firebase connection
- ✅ Set up delivery locations
- ✅ Set up delivery fees
- ✅ Verify data integrity

## 🔍 **Verify the Fix**

1. **Check Firebase Connection**
   ```bash
   python -c "from src.firebase_db import initialize_firebase; print('✅ Success' if initialize_firebase() else '❌ Failed')"
   ```

2. **Test Delivery Locations**
   - Start your bot: `python main.py --bot user`
   - Try ordering from a Bole Area restaurant
   - You should now see delivery locations like:
     - 🚪 Applied Library (20 birr)
     - 🚪 Federal Dorm (20 birr)
     - 🚪 Central Library (30 birr)
     - etc.

## 📊 **Expected Data Structure**

After running the setup script, your Firebase should contain:

### **Areas Collection:**
```json
{
  "areas": [
    {"id": 1, "name": "Bole Area"},
    {"id": 2, "name": "Geda Gate Area"},
    {"id": 3, "name": "Kereyu Area"},
    {"id": 4, "name": "College Mecheresha Area"},
    {"id": 5, "name": "Stadium Area"}
  ]
}
```

### **Delivery Locations Collection:**
```json
{
  "delivery_locations": [
    {"id": 1, "name": "Applied Library"},
    {"id": 2, "name": "Federal Dorm"},
    {"id": 3, "name": "Anfi"},
    {"id": 4, "name": "Central Library"},
    {"id": 5, "name": "Masters Dorm (Lebs Matebiya)"},
    {"id": 6, "name": "B-371 (Fresh)"}
  ]
}
```

### **Delivery Fees Collection:**
```json
{
  "delivery_fees": [
    {"area_id": 1, "location_id": 1, "fee": 20},
    {"area_id": 1, "location_id": 2, "fee": 20},
    {"area_id": 1, "location_id": 3, "fee": 30},
    ...
  ]
}
```

## 🚨 **Troubleshooting**

### **Error: "Firebase credentials not found"**
- ✅ Check that credentials file exists in project root
- ✅ Check that FIREBASE_CREDENTIALS is set in .env
- ✅ Verify file permissions (should be readable)

### **Error: "Permission denied"**
- ✅ Check Firebase security rules
- ✅ Verify service account has proper permissions
- ✅ Check that database URL is correct

### **Error: "No delivery locations found"**
- ✅ Run the setup script: `python setup_firebase_delivery_data.py`
- ✅ Check Firebase console to verify data exists
- ✅ Verify area IDs match between restaurants and delivery fees

## 🔐 **Security Notes**

1. **Never commit credentials to git**
   - The .gitignore already excludes credential files
   - Always use environment variables in production

2. **Rotate credentials regularly**
   - Generate new service account keys monthly
   - Delete old keys from Firebase console

3. **Use minimal permissions**
   - Service account should only have necessary Firebase permissions
   - Review Firebase security rules regularly

## 📞 **Need Help?**

If you're still having issues:

1. **Check the logs** for specific error messages
2. **Verify Firebase project settings** in the console
3. **Test with a simple Firebase operation** first
4. **Check network connectivity** to Firebase

## ✅ **Success Indicators**

You'll know it's working when:
- ✅ No "Firebase credentials not found" errors in logs
- ✅ Delivery locations appear when ordering from Bole Area
- ✅ Delivery fees are calculated correctly
- ✅ Orders can be placed successfully

---

**Next Step**: Run `python setup_firebase_delivery_data.py` to initialize your delivery data!
