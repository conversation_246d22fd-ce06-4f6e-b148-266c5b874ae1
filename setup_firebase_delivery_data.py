#!/usr/bin/env python3
"""
Firebase Delivery Data Setup Script
This script initializes delivery locations and fees in Firebase based on the configuration data.
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.firebase_db import set_data, get_data, initialize_firebase
from src.config import delivery_fees, logger

def setup_delivery_locations():
    """Set up delivery locations in Firebase"""
    print("🚪 Setting up delivery locations...")
    
    # Extract unique delivery locations from the config
    delivery_locations = []
    location_id = 1
    
    # Get all unique location names from the delivery_fees config
    all_locations = set()
    for area_name, locations in delivery_fees.items():
        for location_name in locations.keys():
            all_locations.add(location_name)
    
    # Create delivery locations list
    for location_name in sorted(all_locations):
        delivery_locations.append({
            "id": location_id,
            "name": location_name
        })
        location_id += 1
    
    # Save to Firebase
    delivery_locations_data = {"delivery_locations": delivery_locations}
    success = set_data("delivery_locations", delivery_locations_data)
    
    if success:
        print(f"✅ Successfully created {len(delivery_locations)} delivery locations")
        for location in delivery_locations:
            print(f"   📍 {location['name']} (ID: {location['id']})")
    else:
        print("❌ Failed to create delivery locations")
        return False
    
    return delivery_locations

def setup_delivery_fees(delivery_locations):
    """Set up delivery fees in Firebase"""
    print("\n💰 Setting up delivery fees...")
    
    # Create area mapping
    area_mapping = {
        "Bole Area": 1,
        "Geda Gate Area": 2,
        "Kereyu Area": 3,
        "College Mecheresha Area": 4,
        "Stadium Area": 5
    }
    
    # Create location name to ID mapping
    location_mapping = {loc["name"]: loc["id"] for loc in delivery_locations}
    
    delivery_fees_list = []
    
    # Convert config data to Firebase format
    for area_name, locations in delivery_fees.items():
        area_id = area_mapping.get(area_name)
        if not area_id:
            print(f"⚠️ Warning: Unknown area {area_name}")
            continue
            
        for location_name, fee in locations.items():
            location_id = location_mapping.get(location_name)
            if not location_id:
                print(f"⚠️ Warning: Unknown location {location_name}")
                continue
                
            delivery_fees_list.append({
                "area_id": area_id,
                "location_id": location_id,
                "fee": fee
            })
    
    # Save to Firebase
    delivery_fees_data = {"delivery_fees": delivery_fees_list}
    success = set_data("delivery_fees", delivery_fees_data)
    
    if success:
        print(f"✅ Successfully created {len(delivery_fees_list)} delivery fee entries")
        
        # Group by area for display
        fees_by_area = {}
        for fee_entry in delivery_fees_list:
            area_id = fee_entry["area_id"]
            if area_id not in fees_by_area:
                fees_by_area[area_id] = []
            fees_by_area[area_id].append(fee_entry)
        
        for area_id, fees in fees_by_area.items():
            area_name = next((name for name, id in area_mapping.items() if id == area_id), f"Area {area_id}")
            print(f"   🏢 {area_name}: {len(fees)} locations")
    else:
        print("❌ Failed to create delivery fees")
        return False
    
    return True

def setup_areas():
    """Set up areas in Firebase"""
    print("\n🏢 Setting up areas...")
    
    areas = [
        {"id": 1, "name": "Bole Area"},
        {"id": 2, "name": "Geda Gate Area"},
        {"id": 3, "name": "Kereyu Area"},
        {"id": 4, "name": "College Mecheresha Area"},
        {"id": 5, "name": "Stadium Area"}
    ]
    
    areas_data = {"areas": areas}
    success = set_data("areas", areas_data)
    
    if success:
        print(f"✅ Successfully created {len(areas)} areas")
        for area in areas:
            print(f"   🏢 {area['name']} (ID: {area['id']})")
    else:
        print("❌ Failed to create areas")
        return False
    
    return True

def verify_firebase_data():
    """Verify that the data was set up correctly"""
    print("\n🔍 Verifying Firebase data...")
    
    # Check delivery locations
    locations_data = get_data("delivery_locations")
    if locations_data and "delivery_locations" in locations_data:
        locations = locations_data["delivery_locations"]
        print(f"✅ Found {len(locations)} delivery locations in Firebase")
    else:
        print("❌ No delivery locations found in Firebase")
        return False
    
    # Check delivery fees
    fees_data = get_data("delivery_fees")
    if fees_data and "delivery_fees" in fees_data:
        fees = fees_data["delivery_fees"]
        print(f"✅ Found {len(fees)} delivery fees in Firebase")
    else:
        print("❌ No delivery fees found in Firebase")
        return False
    
    # Check areas
    areas_data = get_data("areas")
    if areas_data and "areas" in areas_data:
        areas = areas_data["areas"]
        print(f"✅ Found {len(areas)} areas in Firebase")
    else:
        print("❌ No areas found in Firebase")
        return False
    
    # Test specific area (Bole Area = ID 1)
    print("\n🧪 Testing Bole Area delivery locations...")
    bole_locations = []
    for location in locations:
        location_id = location["id"]
        location_name = location["name"]
        
        # Find fee for this location in Bole Area (area_id = 1)
        for fee_entry in fees:
            if fee_entry["area_id"] == 1 and fee_entry["location_id"] == location_id:
                bole_locations.append({
                    "id": location_id,
                    "name": location_name,
                    "fee": fee_entry["fee"]
                })
                break
    
    if bole_locations:
        print(f"✅ Found {len(bole_locations)} valid delivery locations for Bole Area:")
        for loc in bole_locations:
            print(f"   📍 {loc['name']} - {loc['fee']} birr")
    else:
        print("❌ No valid delivery locations found for Bole Area")
        return False
    
    return True

def main():
    """Main setup function"""
    print("🚀 Firebase Delivery Data Setup")
    print("=" * 50)
    
    # Initialize Firebase
    print("🔥 Initializing Firebase...")
    if not initialize_firebase():
        print("❌ Failed to initialize Firebase. Please check your credentials.")
        print("\nTo fix this issue:")
        print("1. Make sure you have the Firebase credentials file:")
        print("   wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json")
        print("2. OR set FIREBASE_CREDENTIALS in your .env file")
        print("3. Make sure FIREBASE_DATABASE_URL is correct in .env")
        return False
    
    print("✅ Firebase initialized successfully")
    
    # Set up areas first
    if not setup_areas():
        return False
    
    # Set up delivery locations
    delivery_locations = setup_delivery_locations()
    if not delivery_locations:
        return False
    
    # Set up delivery fees
    if not setup_delivery_fees(delivery_locations):
        return False
    
    # Verify everything was set up correctly
    if not verify_firebase_data():
        return False
    
    print("\n🎉 Firebase delivery data setup completed successfully!")
    print("\nNext steps:")
    print("1. Test the delivery location selection in your bot")
    print("2. Verify that users can see delivery locations for Bole Area")
    print("3. Check that delivery fees are calculated correctly")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
