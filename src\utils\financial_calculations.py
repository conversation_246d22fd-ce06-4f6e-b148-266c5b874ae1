"""
Financial calculation utilities for handling point-based vs cash payments.

This module provides functions to calculate company profit and personnel earnings
that properly distinguish between cash and point payments according to the requirements:
- Delivery personnel receive 50% of delivery fees regardless of payment method
- Company profit excludes delivery fees paid with points
- Only cash portions contribute to company profit calculations
"""

import logging
from typing import Dict, List, Any, Tuple

logger = logging.getLogger(__name__)


def calculate_payment_breakdown(order: Dict[str, Any]) -> Dict[str, float]:
    """
    Calculate payment breakdown for an order, ensuring backward compatibility.
    
    Args:
        order: Order dictionary containing payment information
        
    Returns:
        Dictionary with payment breakdown:
        - cash_amount: Total cash paid
        - points_amount: Total points used
        - delivery_fee_cash: Delivery fee portion paid in cash
        - delivery_fee_points: Delivery fee portion paid in points
        - subtotal: Food subtotal (always cash)
    """
    try:
        subtotal = float(order.get('subtotal', 0))
        delivery_fee = float(order.get('delivery_fee', 0))
        points_used = int(order.get('points_used', 0))
        
        # Check if order already has new payment breakdown fields
        if 'delivery_fee_cash' in order and 'delivery_fee_points' in order:
            return {
                'cash_amount': float(order.get('cash_amount', subtotal + delivery_fee)),
                'points_amount': float(order.get('points_amount', points_used)),
                'delivery_fee_cash': float(order.get('delivery_fee_cash', delivery_fee)),
                'delivery_fee_points': float(order.get('delivery_fee_points', 0)),
                'subtotal': subtotal
            }
        
        # Calculate breakdown for legacy orders
        if points_used > 0:
            # Points were used for delivery fee
            delivery_fee_points = min(points_used, delivery_fee)
            delivery_fee_cash = delivery_fee - delivery_fee_points
            cash_amount = subtotal + delivery_fee_cash
        else:
            # No points used, all cash payment
            delivery_fee_points = 0
            delivery_fee_cash = delivery_fee
            cash_amount = subtotal + delivery_fee
            
        return {
            'cash_amount': cash_amount,
            'points_amount': float(points_used),
            'delivery_fee_cash': delivery_fee_cash,
            'delivery_fee_points': delivery_fee_points,
            'subtotal': subtotal
        }
        
    except (ValueError, TypeError) as e:
        logger.error(f"Error calculating payment breakdown for order: {e}")
        # Return safe defaults
        subtotal = float(order.get('subtotal', 0))
        delivery_fee = float(order.get('delivery_fee', 0))
        return {
            'cash_amount': subtotal + delivery_fee,
            'points_amount': 0,
            'delivery_fee_cash': delivery_fee,
            'delivery_fee_points': 0,
            'subtotal': subtotal
        }


def calculate_personnel_earnings(orders: List[Dict[str, Any]]) -> float:
    """
    Calculate total personnel earnings from a list of orders.
    Personnel get 50% of ALL delivery fees regardless of payment method.
    
    Args:
        orders: List of order dictionaries
        
    Returns:
        Total personnel earnings
    """
    total_earnings = 0.0
    
    for order in orders:
        try:
            delivery_fee = float(order.get('delivery_fee', 0))
            # Personnel get 50% of delivery fee regardless of payment method
            total_earnings += delivery_fee * 0.5
        except (ValueError, TypeError) as e:
            logger.error(f"Error calculating personnel earnings for order {order.get('order_number', 'unknown')}: {e}")
            continue
            
    return total_earnings


def calculate_company_profit(orders: List[Dict[str, Any]]) -> Dict[str, float]:
    """
    Calculate company profit from a list of orders with point payment cost accounting.
    Point-paid delivery fees are treated as company costs/expenses.

    Args:
        orders: List of order dictionaries

    Returns:
        Dictionary with profit breakdown:
        - total_delivery_fees: Total delivery fees collected
        - cash_delivery_fees: Delivery fees paid in cash (revenue)
        - points_delivery_fees: Delivery fees paid in points (company cost)
        - cash_revenue: 50% of cash delivery fees (company revenue)
        - point_payment_costs: 50% of point-paid delivery fees (company expense)
        - company_profit: cash_revenue - point_payment_costs (can be negative)
        - personnel_earnings: 50% of all delivery fees
    """
    total_delivery_fees = 0.0
    cash_delivery_fees = 0.0
    points_delivery_fees = 0.0

    for order in orders:
        try:
            breakdown = calculate_payment_breakdown(order)

            delivery_fee = float(order.get('delivery_fee', 0))
            delivery_fee_cash = breakdown['delivery_fee_cash']
            delivery_fee_points = breakdown['delivery_fee_points']

            total_delivery_fees += delivery_fee
            cash_delivery_fees += delivery_fee_cash
            points_delivery_fees += delivery_fee_points

        except (ValueError, TypeError) as e:
            logger.error(f"Error calculating company profit for order {order.get('order_number', 'unknown')}: {e}")
            continue

    # Company revenue from cash delivery fees (50% share)
    cash_revenue = cash_delivery_fees * 0.5

    # Company cost from point-paid delivery fees (50% still paid to personnel)
    point_payment_costs = points_delivery_fees * 0.5

    # Net company profit = revenue - costs (can be negative)
    company_profit = cash_revenue - point_payment_costs

    # Personnel earnings are 50% of ALL delivery fees (unchanged)
    personnel_earnings = total_delivery_fees * 0.5

    return {
        'total_delivery_fees': total_delivery_fees,
        'cash_delivery_fees': cash_delivery_fees,
        'points_delivery_fees': points_delivery_fees,
        'cash_revenue': cash_revenue,
        'point_payment_costs': point_payment_costs,
        'company_profit': company_profit,
        'personnel_earnings': personnel_earnings
    }


def calculate_revenue_breakdown(orders: List[Dict[str, Any]]) -> Dict[str, float]:
    """
    Calculate comprehensive revenue breakdown from a list of orders with cost accounting.

    Args:
        orders: List of order dictionaries

    Returns:
        Dictionary with revenue breakdown:
        - food_revenue: Total food sales (always cash)
        - total_cash_revenue: Total cash collected
        - total_points_used: Total points used
        - delivery_fees_cash: Delivery fees paid in cash (revenue)
        - delivery_fees_points: Delivery fees paid in points (company cost)
        - cash_revenue: Company revenue from cash delivery fees
        - point_payment_costs: Company costs from point-paid delivery fees
        - company_profit: Net profit (cash_revenue - point_payment_costs, can be negative)
        - personnel_earnings: Personnel's share (50% of all delivery fees)
    """
    food_revenue = 0.0
    total_cash_revenue = 0.0
    total_points_used = 0.0
    delivery_fees_cash = 0.0
    delivery_fees_points = 0.0

    for order in orders:
        try:
            breakdown = calculate_payment_breakdown(order)

            food_revenue += breakdown['subtotal']
            total_cash_revenue += breakdown['cash_amount']
            total_points_used += breakdown['points_amount']
            delivery_fees_cash += breakdown['delivery_fee_cash']
            delivery_fees_points += breakdown['delivery_fee_points']

        except (ValueError, TypeError) as e:
            logger.error(f"Error calculating revenue breakdown for order {order.get('order_number', 'unknown')}: {e}")
            continue

    # Calculate profit breakdown with cost accounting
    profit_data = calculate_company_profit(orders)

    return {
        'food_revenue': food_revenue,
        'total_cash_revenue': total_cash_revenue,
        'total_points_used': total_points_used,
        'delivery_fees_cash': delivery_fees_cash,
        'delivery_fees_points': delivery_fees_points,
        'cash_revenue': profit_data['cash_revenue'],
        'point_payment_costs': profit_data['point_payment_costs'],
        'company_profit': profit_data['company_profit'],
        'personnel_earnings': profit_data['personnel_earnings']
    }


def format_financial_summary(revenue_breakdown: Dict[str, float]) -> str:
    """
    Format financial summary for display in bot messages with cost accounting.

    Args:
        revenue_breakdown: Revenue breakdown from calculate_revenue_breakdown()

    Returns:
        Formatted string for display
    """
    profit_status = "📈" if revenue_breakdown['company_profit'] >= 0 else "📉"

    # Format numbers without escaping decimal points
    food_revenue = f"{revenue_breakdown['food_revenue']:.2f}"
    delivery_fees_cash = f"{revenue_breakdown['delivery_fees_cash']:.2f}"
    delivery_fees_points = f"{revenue_breakdown['delivery_fees_points']:.0f}"
    total_cash_revenue = f"{revenue_breakdown['total_cash_revenue']:.2f}"
    total_points_used = f"{revenue_breakdown['total_points_used']:.0f}"
    cash_revenue = f"{revenue_breakdown['cash_revenue']:.2f}"
    point_payment_costs = f"{revenue_breakdown['point_payment_costs']:.2f}"
    company_profit = f"{revenue_breakdown['company_profit']:.2f}"
    personnel_earnings = f"{revenue_breakdown['personnel_earnings']:.2f}"

    return f"""💰 **Financial Summary:**
• Food Revenue: {food_revenue} birr (cash)
• Delivery Fees (Cash): {delivery_fees_cash} birr
• Delivery Fees (Points): {delivery_fees_points} points
• Total Cash Revenue: {total_cash_revenue} birr
• Total Points Used: {total_points_used} points

{profit_status} **Profit/Loss Analysis:**
• Cash Revenue: {cash_revenue} birr (50% of cash delivery fees)
• Point Payment Costs: {point_payment_costs} birr (50% of point delivery fees)
• **Net Company Profit**: {company_profit} birr
• Personnel Earnings: {personnel_earnings} birr (50% of all delivery fees)

💡 **Note**: Point payments are company costs since personnel still receive their 50% share."""


def update_order_payment_breakdown(order: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update an order dictionary with payment breakdown fields for backward compatibility.
    
    Args:
        order: Order dictionary to update
        
    Returns:
        Updated order dictionary with payment breakdown fields
    """
    breakdown = calculate_payment_breakdown(order)
    
    # Update order with new fields
    order['cash_amount'] = breakdown['cash_amount']
    order['points_amount'] = breakdown['points_amount']
    order['delivery_fee_cash'] = breakdown['delivery_fee_cash']
    order['delivery_fee_points'] = breakdown['delivery_fee_points']
    
    return order
